# L2W（Lynx-to-Web）渲染引擎技术方案

## 1. 综述

本方案旨在设计一个在浏览器环境中运行的 **L2W (Lynx-to-Web) 渲染引擎**，用于实时、高保真地预览 Lynx 应用界面。该引擎将严格遵循 `prompts` 文件夹中定义的 Lynx 技术规范，包括组件系统、样式约束、框架核心和 API 行为，确保 Web 端的渲染效果与原生 Lynx 环境高度一致，为 **Lynx AI Studio** 提供核心的在线预览能力。

## 2. 核心设计原则

- **高保真度**: 严格遵循 `TTML`、`TTSS` 和 `LynxFrameworkCore` 的约束，确保组件行为、样式渲染和 API 调用在 Web 端的表现与原生 Lynx 一致。
- **组件化架构**: 将每个 Lynx 组件（如 `view`, `text`, `scroll-view`）映射到一个独立的 Web Component 或 React/Vue 组件。
- **CSS-in-JS**: 动态处理 `TTSS` 样式，将其转换为 Web 兼容的 CSS，并解决 `rpx` 单位转换、样式隔离等问题。
- **API 模拟层**: 实现一个与 Lynx 标准一致的 API 模拟层（`lynx.*`），处理数据管理、导航、网络和系统信息等。
- **插件化扩展**: 针对 `LightCharts` 等复杂组件，设计插件化加载和渲染机制。

## 3. 详细技术方案

### 3.1. TTML 到 Web DOM 的转换

L2W 引擎的核心是将 TTML 结构树实时转换为 Web DOM 树。这需要一个解析器和一组对应的 Web 组件。

**3.1.1. 组件映射**

| Lynx 组件 (`TTML`) | Web 实现方式 | 关键实现细节 |
| :--- | :--- | :--- |
| `view` | `div` | 实现 Flexbox 布局，处理 `bindtap` 等事件。 |
| `text` | `span` | 严格处理文本内容，不支持子节点嵌套。实现 XML 实体转义（`&lt;`, `&gt;` 等）。 |
| `image` | `img` | 实现自闭合标签的逻辑，处理 `src` 和 `mode` 属性。 |
| `scroll-view` | `div` with `overflow` | 必须设置 `overflow-y: scroll` 或 `overflow-x: scroll`。模拟下拉刷新和上拉加载事件。 |
| `input` | `input` | 映射 `placeholder`、`value` 等属性，模拟 `bindinput` 事件。 |
| `swiper` | 使用 [Swiper.js](https://swiperjs.com/) | 动态创建 `swiper-container` 和 `swiper-slide`，映射 `indicator-dots`、`autoplay` 等属性。 |
| `lightcharts-canvas` | `canvas` + [ECharts](https://echarts.apache.org/en/index.html) | **见 3.4. LightCharts 插件化方案** |
| `button` | `button` 或 `div` | 推荐使用 `div` + `role="button"` 以便更好地控制样式，同时绑定 `click` 事件。 |
| **其他组件** | 相应 HTML 标签 | 如 `video`, `textarea`, `switch` 等，都将映射到其对应的标准 HTML5 标签，并根据 `prompts` 规范约束其行为和样式。 |

**3.1.2. 属性与事件绑定**

- **数据绑定**: 实现一个简单的模板引擎，解析 `{{ }}` 语法，将 `data` 中的数据渲染到 DOM 属性和文本节点中。
- **事件转换**: 将 `bindtap` 转换为 `onclick`，`bindscroll` 转换为 `onscroll`。实现事件冒泡（`bind`）和捕获（`catch`）的逻辑差异。
- **条件与循环**: 实现 `tt:if` 和 `tt:for` 的逻辑。`tt:if` 控制 DOM 元素的添加和移除，`tt:for` 则用于遍历数组并生成对应的 DOM 元素列表。

### 3.2. TTSS 到 Web CSS 的转换

这是保证视觉高保真度的关键。我们将采用 CSS-in-JS 的方案来动态生成和应用样式。

**3.2.1. 单位转换 (RPX)**

`rpx` 是一个响应式单位，`750rpx` 等于屏幕宽度。在 Web 端，我们可以用 `vw` (viewport width) 来模拟。

```javascript
function rpxToVw(rpxValue) {
  return (rpxValue / 7.5) + 'vw';
}

// 或者基于一个固定的预览宽度（例如 375px）
const PREVIEW_WIDTH = 375;
function rpxToPx(rpxValue) {
  return (rpxValue / 750) * PREVIEW_WIDTH + 'px';
}
```

**3.2.2. 样式处理与注入**

- **解析器**: 解析 TTSS 文件，构建一个 CSSOM (CSS Object Model)。
- **约束校验**: 在解析过程中，严格校验 `TTSSStrictConstraints.ts` 中定义的规则，**忽略或警告**所有禁用的 CSS 属性（如 `backdrop-filter`, `display: grid`）和选择器（如多类选择器 `.a.b`）。
- **动态注入**: 使用 `styled-components` 或 `JSS` 等库，将处理后的样式对象动态注入到每个 Web 组件中，实现组件级别的样式隔离。
- **选择器转换**: 将 TTSS 的后代选择器（如 `.parent view`）转换为 Web 兼容的选择器。

### 3.3. Lynx API 模拟层

在 Web 环境中提供一个全局的 `lynx` 对象，模拟原生 API 的行为。

```javascript
window.lynx = {
  request: (options) => fetch(options.url, { ... }), // 使用 fetch 模拟网络请求
  navigateTo: (options) => { /* 模拟页面栈导航 */ },
  navigateBack: () => { /* 模拟返回 */ },
  // ... 其他 API
};

window.SystemInfo = {
  platform: 'web',
  pixelRatio: window.devicePixelRatio,
  screenWidth: window.innerWidth,
  // ... 其他系统信息
};
```

- **生命周期**: 模拟 `Card` 对象的生命周期方法，如 `onLoad`, `onShow`, `onReady` 等。
- **`setData`**: 实现一个 `setData` 方法，当数据更新时，能够触发 Web 组件的重新渲染（在 React/Vue 中，这将调用 `setState` 或更新响应式数据）。

### 3.4. LightCharts 插件化方案

根据 `LightChartPromptLoader.md` 的详细文档，我们可以设计一个专门的插件来处理 `lightcharts-canvas` 组件。

**3.4.1. 核心思路：ECharts 替代**

`LightCharts` 在 Lynx 环境中扮演的角色与 `ECharts` 在 Web 环境中高度相似。因此，L2W 引擎将使用 **Apache ECharts** 作为其在 Web 端的渲染后端。

**3.4.2. 实现步骤**

1.  **组件注册**: 当 L2W 引擎遇到 `lightcharts-canvas` TTML 标签时，它会渲染一个带有 `canvas` 元素的 `div` 容器。
2.  **初始化**: 监听 `bindinitchart` 事件。当该事件触发时，获取 `canvas` 引用，并使用 ECharts 进行初始化。
    ```javascript
    // L2W 引擎内部的 LightCharts 插件
    const lightChartComponent = {
      init(lynxEvent) {
        const canvasElement = document.querySelector(`[canvasName="${lynxEvent.detail.canvasName}"]`);
        const chart = echarts.init(canvasElement);
        
        // 将 ECharts 实例存起来，模拟 getChartByCanvasName
        this.charts[lynxEvent.detail.canvasName] = chart;

        // 调用开发者定义的 initChart 方法
        developerCode.initChart({ detail: chart }); 
      }
    };
    ```
3.  **`setOption` 适配**: ECharts 的 `setOption` 与 LightCharts 的 API 非常接近。L2W 引擎将直接将 `option` 对象传递给 ECharts 实例。同时，会根据文档强制执行一些规则：
    - 自动将 `tooltip.useHTML` 设置为 `false`。
    - 检查 `series.data` 的格式，确保其为二维数组。
4.  **API 映射**: 将 LightCharts 的 API（如 `resize`, `on`, `off`, `destroy`）直接映射到对应的 ECharts API。

## 4. 实施路线图

1.  **阶段一：核心渲染引擎**
    -   实现 TTML 解析器和基础组件映射（`view`, `text`, `image`）。
    -   完成 TTSS 解析器和 `rpx` 到 `px` 的转换。
    -   构建基础的 API 模拟层（`setData`, `SystemInfo`）。
2.  **阶段二：高级组件与 API**
    -   实现 `scroll-view`, `swiper` 等复杂容器组件。
    -   完善导航（`navigateTo`）和网络（`request`）API。
    -   实现完整的事件绑定系统。
3.  **阶段三：插件化与优化**
    -   开发 `LightCharts` 插件，集成 ECharts。
    -   进行性能优化，确保大规模 DOM 操作的流畅性。
    -   编写详尽的开发者文档和示例。

## 5. 结论

该 L2W 渲染引擎方案技术上完全可行。通过严格遵循 `prompts` 文件夹中的规范，并巧妙利用成熟的 Web 技术（如 Web Components, ECharts, Swiper.js），我们可以构建一个高保真的 Lynx 在线预览环境，为 **Lynx AI Studio** 的成功奠定坚实的基础。