import React, { useState, useCallback } from 'react';
import Icon from './Icon';

/**
 * 🎯 优化版查询输入面板
 * 
 * 核心改进：
 * 1. 移除冗余的欢迎界面和中间状态
 * 2. 自适应高度替代固定尺寸  
 * 3. 简化按钮结构，使用CSS类替代内联样式
 * 4. 合并重复的功能组件
 */

interface OptimizedQueryInputProps {
  inputText: string;
  onInputChange: (text: string) => void;
  queries: string[];
  isProcessing?: boolean;
  onStartProcess?: () => void;
}

export const OptimizedQueryInput: React.FC<OptimizedQueryInputProps> = ({
  inputText,
  onInputChange,
  queries,
  isProcessing = false,
  onStartProcess,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handlePasteSample = useCallback(() => {
    const samples = [
      '9乘法表的规律和记忆方法',
      '中国历史朝代更替的主要原因', 
      '世界人口最多的十个国家',
      '如何写出吸引人的社交媒体文案',
      '小学数学教学中的趣味方法',
    ];
    onInputChange(samples.join('\n'));
  }, [onInputChange]);

  const handleClearInput = useCallback(() => {
    onInputChange('');
  }, [onInputChange]);

  return (
    <div className="optimized-query-input">
      {/* 紧凑的输入区域 */}
      <div className="input-section">
        <div className="input-header">
          <h3 className="input-title">
            <Icon type="edit" size="sm" />
            查询输入 
            {queries.length > 0 && (
              <span className="query-count">{queries.length} 个</span>
            )}
          </h3>
          
          <div className="input-actions">
            <button 
              className="btn-compact btn-sample"
              onClick={handlePasteSample}
              disabled={isProcessing}
            >
              <Icon type="lightning" size="xs" />
              示例
            </button>
            
            <button 
              className="btn-compact btn-clear"
              onClick={handleClearInput}
              disabled={!inputText || isProcessing}
            >
              <Icon type="trash" size="xs" />
              清除
            </button>
          </div>
        </div>

        {/* 自适应高度输入框 */}
        <textarea
          className={`input-field ${isFocused ? 'focused' : ''} ${isProcessing ? 'processing' : ''}`}
          placeholder="输入查询列表，每行一个或逗号、分号分隔..."
          value={inputText}
          onChange={(e) => onInputChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={isProcessing}
          style={{
            minHeight: '80px',
            maxHeight: '240px', // 自适应高度
            resize: 'vertical'
          }}
        />

        {/* 简化的查询预览 */}
        {queries.length > 0 && (
          <div className="query-preview-compact">
            <div className="preview-items">
              {queries.slice(0, 3).map((query, index) => (
                <span key={index} className="preview-item">
                  {index + 1}. {query.length > 30 ? `${query.slice(0, 30)}...` : query}
                </span>
              ))}
              {queries.length > 3 && (
                <span className="preview-more">+{queries.length - 3} 更多</span>
              )}
            </div>
          </div>
        )}

        {/* 主要操作按钮 */}
        {queries.length > 0 && (
          <button
            className={`btn-primary-action ${isProcessing ? 'processing' : ''}`}
            onClick={onStartProcess}
            disabled={isProcessing}
          >
            <Icon 
              type={isProcessing ? 'processing' : 'lightning'} 
              size="sm"
              animate={isProcessing}
            />
            {isProcessing ? '处理中...' : `开始处理 ${queries.length} 个查询`}
          </button>
        )}
      </div>
    </div>
  );
};

export default OptimizedQueryInput;